<script>
import { firmDetail } from "@/api/firmManage";

export default {
  name: "FirmManageDetail",
  data() {
    return {
      id: "",
      enterName: "",
      activeMenuItem: "sewageEnterprise",
      activeCollapse: ["online_monitoring"], // 默认展开在线监控数据
      data: null,
      loading: false,
      menuItems: [
        {
          id: "sewageEnterprise",
          name: "排污单位基本情况"
        },
        {
          id: "capacityInfoList",
          name: "排污单位登记信息-主要产品及产能补充"
        },
        {
          id: "zlssInfoList",
          name: "排污单位登记信息-排污节点及污染治理设施"
        },
        {
          id: "airOutletBasicInfosList",
          name: "大气污染物排放信息-排放口"
        },
        {
          id: "fswrwQcEmissInfo",
          name: "大气污染物排放信息-企业大气排放总许可量"
        },
        {
          id: "mainOutletEmissInfo",
          name: "水污染物排放信息-排放口"
        },
        {
          id: "fqwrwQcxk",
          name: "水污染物排放信息-申请排放信息"
        },
        {
          id: "wasteSolidEmissionList",
          name: "固体废弃物污染物排放信息-申请排放信息"
        },
        {
          id: "selfMonitorInfoList",
          name: "环境管理要求-自行监测要求"
        },
        {
          id: "online_monitoring",
          name: "在线监控数据",
          children: [
            {
              id: "waste_gas",
              name: "废气"
            },
            {
              id: "waste_water",
              name: "废水"
            },
            {
              id: "monitoring",
              name: "监控"
            }
          ]
        }
      ]
    };
  },
  created() {
    const id = this.$route.params.id;
    const enterName = this.$route.query.enterName;
    if (!id || !enterName) {
      this.goBack();
      return;
    }

    this.id = id;
    this.enterName = enterName;
    this.handleQuery();
  },
  computed: {
    getCurrentMenuName() {
      for (const item of this.menuItems) {
        if (item.id === this.activeMenuItem) {
          return item.name;
        }
        if (item.children) {
          const subItem = item.children.find(
            child => child.id === this.activeMenuItem
          );
          if (subItem) {
            return subItem.name;
          }
        }
      }
      return "";
    }
  },
  methods: {
    handleQuery() {
      this.loading = true
      firmDetail(this.id).then(res => {
        this.data = res.data.data
      }).finally(() => {
        this.loading = false
      })
    },
    // 返回列表页
    goBack() {
      this.$router.replace({ name: "FirmManage" });
    },
    // 点击左侧菜单项
    handleMenuClick(itemId, itemName) {
      this.activeMenuItem = itemId;

    }
  }
};
</script>

<template>
  <div v-loading="loading" class="firm-detail-container">
    <!-- 顶部标题和返回按钮 -->
    <div class="header-section">
      <div class="title-wrapper">
        <el-button
          type="text"
          icon="el-icon-arrow-left"
          @click="goBack"
          class="back-btn"
        >
          返回
        </el-button>
        <h2 class="page-title">{{ enterName }}</h2>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧菜单列表 -->
      <div class="left-menu">
        <div class="menu-list">
          <template v-for="item in menuItems">
            <!-- 普通菜单项 -->
            <div
              v-if="!item.children"
              :key="item.id"
              class="menu-item"
              :class="{ active: activeMenuItem === item.id }"
              @click="handleMenuClick(item.id, item.name)"
            >
              {{ item.name }}
            </div>
            <!-- 带子菜单的项 -->
            <div v-else class="menu-group">
              <el-collapse v-model="activeCollapse" accordion>
                <el-collapse-item :name="item.id">
                  <template slot="title">
                    <div class="collapse-title">
                      <span>{{ item.name }}</span>
                    </div>
                  </template>
                  <div class="sub-menu-collapse">
                    <div
                      v-for="subItem in item.children"
                      :key="subItem.id"
                      class="sub-menu-item"
                      :class="{ active: activeMenuItem === subItem.id }"
                      @click="handleMenuClick(subItem.id, subItem.name)"
                    >
                      {{ subItem.name }}
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </template>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <div class="content-placeholder">
          <p>当前选中：{{ getCurrentMenuName }}</p>
          <p>右侧表格内容暂未实现</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.firm-detail-container {
  padding: 16px;

  .header-section {
    padding: 16px 20px;
    background: #ffffff;
    border-bottom: 1px solid #e8e8e8;

    .title-wrapper {
      display: flex;
      align-items: center;

      .back-btn {
        padding: 0;
        margin-right: 16px;
        font-size: 14px;
        color: #666666;

        &:hover {
          color: #409eff;
        }
      }

      .page-title {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
        color: #333333;
      }
    }
  }

  .main-content {
    display: flex;
    height: calc(100vh - 168px);

    .left-menu {
      width: 300px;
      overflow-y: auto;
      background: #ffffff;
      border-right: 1px solid #e8e8e8;

      .menu-list {
        line-height: 25px;

        .menu-item {
          padding: 12px;
          font-size: 14px;
          color: #333333;
          cursor: pointer;
          border-right: 3px solid transparent;
          border-bottom: 1px solid #f0f0f0;
          transition: all 0.3s;

          &:hover {
            color: #409eff;
            background-color: #f5f7fa;
          }

          &.active {
            color: #409eff;
            background-color: #e6f7ff;
            border-right-color: #409eff;
          }
        }

        .menu-group {
          border-bottom: 1px solid #f0f0f0;

          // 自定义 el-collapse 样式
          ::v-deep .el-collapse {
            border: none;

            .el-collapse-item {
              &:last-child {
                border-bottom: none;
              }

              .el-collapse-item__header {
                height: auto;
                padding: 0;
                line-height: normal;
                background-color: #fafafa;
                border-bottom: none;

                &:hover {
                  background-color: #f0f0f0;
                }

                .collapse-title {
                  display: flex;
                  align-items: center;
                  padding: 12px;
                  font-size: 14px;
                  font-weight: 500;
                  color: #333333;
                }
              }

              .el-collapse-item__wrap {
                border-bottom: none;

                .el-collapse-item__content {
                  padding: 0;

                  .sub-menu-collapse {
                    .sub-menu-item {
                      padding: 10px 20px 10px 30px;
                      font-size: 14px;
                      color: #666666;
                      cursor: pointer;
                      border-bottom: 1px solid #f8f8f8;
                      transition: all 0.3s;

                      &:hover {
                        color: #409eff;
                        background-color: #f5f7fa;
                      }

                      &.active {
                        color: #409eff;
                        background-color: #e6f7ff;
                        border-right: 3px solid #409eff;
                      }

                      &:last-child {
                        border-bottom: none;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .right-content {
      flex: 1;
      margin: 10px 0 0 10px;
      background: #ffffff;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .content-placeholder {
        padding: 40px;
        color: #999999;
        text-align: center;

        p {
          margin: 16px 0;
          font-size: 16px;
        }
      }
    }
  }
}
</style>
