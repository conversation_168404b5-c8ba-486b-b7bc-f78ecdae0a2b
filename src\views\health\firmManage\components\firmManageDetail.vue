<script>
export default {
  name: 'FirmManageDetail',
  created() {
    const id = this.$route.params.id;
    const enterName = this.$route.params.enterName;
    if (!id || !enterName) {
      this.$router.replace({ name: 'FirmManage' });
      return
    }

    this.handleQuery()
  },
  methods: {
    handleQuery() {

    }
  }
}
</script>

<template>
<div>详情</div>
</template>

<style scoped lang="scss">

</style>
