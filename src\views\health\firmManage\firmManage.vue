<template>
  <div class="content">
    <div class="search">
      <CommonHeader />
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item>
          <el-cascader
            v-model="searchForm.divisionCodeList"
            :options="divisionList"
            :show-all-levels="false"
            :props="{ children: 'streets', value: 'code', label: 'name' }"
            placeholder="行政区划"
            collapse-tags
            clearable
            @change="getList(1)"
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item>
          <el-cascader
            v-model="searchForm.industryId"
            :options="industryList"
            :show-all-levels="false"
            :props="{
              emitPath: false,
              children: 'childList',
              value: 'id',
              label: 'name'
            }"
            placeholder="所属行业"
            collapse-tags
            clearable
            @change="getList(1)"
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item>
          <el-input
            clearable
            v-model="searchForm.keywords"
            placeholder="请输入内容"
            @change="getList(1)"
            style="width: 300px;"
          ></el-input>
        </el-form-item>
        <el-form-item style="float: right;">
          <el-button @click="reset">重置</el-button>
          <el-button
            type="primary"
            @click="handleExport"
            :loading="exportLoading"
            >导出</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="table">
      <el-table
        ref="firmTable"
        v-loading="loading"
        :data="tableData"
        class="tableCustomStyle"
        border
        :header-cell-style="{ background: '#F5F7FA' }"
      >
        <el-table-column
          label="序号"
          min-width="100"
          type="index"
          align="center"
        />
        <el-table-column
          label="统一社会信用代码"
          align="center"
          prop="creditCode"
          min-width="180"
        />
        <el-table-column
          label="企业名称"
          align="center"
          prop="enterName"
          min-width="200"
        />
        <el-table-column
          label="行业类别"
          align="center"
          prop="industryName"
          min-width="120"
        />
        <el-table-column
          label="所属区域"
          align="center"
          prop="districtName"
          min-width="100"
        />
        <el-table-column
          label="所属街道"
          align="center"
          prop="streetName"
          min-width="100"
        />
        <el-table-column
          label="生产经营场所地址"
          align="center"
          prop="opeAddress"
          min-width="250"
        />
        <el-table-column
          label="技术负责人"
          align="center"
          prop="concatPerson"
          min-width="120"
        />
        <el-table-column
          label="联系人电话"
          align="center"
          prop="mobilePhone"
          min-width="130"
        />
        <el-table-column
          label="投产日期"
          align="center"
          prop="operaTime"
          min-width="130"
        />

        <el-table-column
          label="操作"
          align="center"
          min-width="100"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handleDetail(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        style=" display: flex; justify-content: flex-end; margin-top: 15px;"
        background
        :current-page.sync="pageNum"
        :page-size="pageSize"
        layout="prev, pager, next, jumper"
        :total="total"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { firmExport, firmPageList } from "@/api/firmManage";
import { doDivisionList, getIndustryList } from "@/api/pollutionSourceSurveyChecklist/pollutionSourceSurveyChecklist";
import CommonHeader from "@/components/commonHeader/index.vue";

export default {
  name: "FirmManage",
  components: {
    CommonHeader
  },
  data() {
    return {
      searchForm: {
        keywords: "",
        streetCode: "",
        industryCodes: "",
        divisionCodeList: [],
        industryId: ""
      },
      divisionList: [],
      industryList: [],
      tableData: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      exportLoading: false,
      detailData: null
    };
  },
  created() {
    this.getList();
    this.getDivisionList();
    this.getIndustryList();
  },
  methods: {
    async getList(pageNum) {
      this.loading = true;
      try {
        const res = await firmPageList({
          keywords: this.searchForm.keywords,
          streetCode:
            this.searchForm.divisionCodeList.length > 0
              ? this.searchForm.divisionCodeList[1]
              : this.searchForm.streetCode,
          // districtId:
          //   this.searchForm.divisionCodeList.length > 0
          //     ? this.searchForm.divisionCodeList[0]
          //     : "",
          industryCodes:
            this.searchForm.industryId || this.searchForm.industryCodes,
          pageNum: pageNum || this.pageNum,
          pageSize: this.pageSize
        });
        this.tableData = res.data.data.records || [];
        this.total = res.data.data.total || 0;
        this.pageNum = res.data.data.current || 1;
      } catch (error) {
        console.error("获取企业列表失败:", error);
        this.$message.error("获取企业列表失败");
      } finally {
        this.loading = false;
      }
    },
    handleCurrentChange(page) {
      this.getList(page);
    },
    reset() {
      this.searchForm = {
        keywords: "",
        streetCode: "",
        industryCodes: "",
        divisionCodeList: [],
        industryId: ""
      };
      this.getList(1);
    },
    async handleDetail(row) {
      this.$router.push({
        name: "FirmManageDetail",
        params: { id: row.id, enterName: row.enterName }
      });
    },
    async handleExport() {
      this.exportLoading = true;
      try {
        const res = await firmExport({
          keywords: this.searchForm.keywords,
          streetCode:
            this.searchForm.divisionCodeList.length > 0
              ? this.searchForm.divisionCodeList[1]
              : this.searchForm.streetCode,
          districtId:
            this.searchForm.divisionCodeList.length > 0
              ? this.searchForm.divisionCodeList[0]
              : "",
          industryCodes:
            this.searchForm.industryId || this.searchForm.industryCodes
        });
        // 下载二进制流
        const blob = new Blob([res.data], {
          type: "application/vnd.ms-excel"
        });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "企业信息.xlsx";
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败");
      } finally {
        this.exportLoading = false;
      }
    },
    // 获取行政区划列表
    async getDivisionList() {
      try {
        const res = await doDivisionList();
        this.divisionList = res.data.data || [];
      } catch (error) {
        console.error("获取行政区划列表失败:", error);
      }
    },
    // 获取行业列表
    async getIndustryList() {
      try {
        const res = await getIndustryList();
        this.industryList = res.data.data || [];
      } catch (error) {
        console.error("获取行业列表失败:", error);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  padding: 15px;
  .search {
    padding: 15px;
    background: #ffffff;
  }
}
.table {
  padding: 15px;
  margin-top: 15px;
  background: #ffffff;
}
</style>
